import Message from 'tdesign-miniprogram/message/index';
import { 
  apiGetSingleParkingSpotRevenueAmount, 
  apiGetSingleParkingSpotRevenueList 
} from '../../api/earning';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 车锁ID
    lockId: '',
    // 车位总收益金额
    totalLockRevenue: '0.00',
    // 车位信息
    spotInfo: null,
    // 收益列表
    incomeList: [],
    // 是否正在加载
    loading: false,
    // 是否正在刷新
    refreshing: false,
    // 是否有更多数据
    hasMoreData: true,
    // 当前页码
    currentPage: 1,
    // 每页数量
    pageSize: 10,
    // 加载配置
    loadingProps: {
      size: '50rpx',
    },
    // 页面滚动位置
    scrollTop: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { lockId } = options;
    if (lockId) {
      this.setData({ lockId });
      this.fetchData();
    } else {
      this.showMessage('缺少车锁ID参数', 'error');
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时，确保浮动头部状态正确
    setTimeout(() => {
      this.resetFloatingHeader();
    }, 300);
  },

  /**
   * 重置浮动头部状态
   */
  resetFloatingHeader() {
    const query = wx.createSelectorQuery();
    query.select('#floatingHeader').node();
    query.exec((res) => {
      if (res && res[0] && res[0].node) {
        const headerComponent = res[0].node.component;
        if (headerComponent && typeof headerComponent.setCollapsed === 'function') {
          // 先设置为展开状态
          headerComponent.setCollapsed(false);

          // 如果页面已经滚动，则根据滚动位置设置折叠状态
          if (this.data.scrollTop > 80) {
            headerComponent.setCollapsed(true);
          }
        }
      }
    });
  },

  /**
   * 获取数据
   */
  async fetchData(isRefresh = false) {
    // 如果是刷新，重置页码
    if (isRefresh) {
      this.setData({
        currentPage: 1,
        hasMoreData: true,
        incomeList: []
      });
    }

    // 如果没有更多数据，直接返回
    if (!this.data.hasMoreData && !isRefresh) {
      return;
    }

    // 设置加载状态
    this.setData({ loading: true });

    try {
      const { lockId, currentPage, pageSize } = this.data;
      
      // 获取车位总收益金额
      if (isRefresh || currentPage === 1) {
        const totalLockRevenue = await apiGetSingleParkingSpotRevenueAmount({ lockId });
        this.setData({ totalLockRevenue });
      }

      // 获取车位收益列表
      const listData = await apiGetSingleParkingSpotRevenueList({
        lockId,
        current: currentPage,
        size: pageSize,
        sort: 'id',
        order: 'descending'
      });

      const newList = isRefresh ? listData.results : [...this.data.incomeList, ...listData.results];
      const hasMore = parseInt(listData.curPage) < parseInt(listData.pages) - 1;

      // 从第一条记录中获取车位信息
      let spotInfo = this.data.spotInfo;
      if (newList.length > 0 && !spotInfo) {
        const firstItem = newList[0];
        spotInfo = {
          parkName: firstItem.parkName,
          location: firstItem.location,
          code: firstItem.code
        };
      }

      this.setData({
        incomeList: newList,
        hasMoreData: hasMore,
        currentPage: currentPage + 1,
        spotInfo
      });
    } catch (error) {
      console.error('获取数据失败:', error);
      this.showMessage('获取数据失败，请重试', 'error');
    } finally {
      this.setData({ 
        loading: false, 
        refreshing: false 
      });
    }
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({ refreshing: true });
    this.fetchData(true);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (!this.data.loading) {
      this.fetchData();
    }
  },

  /**
   * 页面滚动事件的处理函数
   */
  onPageScroll(e) {
    // 保存滚动位置
    this.setData({ scrollTop: e.scrollTop });
  },

  /**
   * 点击收益项
   */
  onItemTap(e) {
    const { item } = e.currentTarget.dataset;
    // 可以跳转到订单详情页面
    console.log('点击收益项:', item);
  },

  /**
   * 返回按钮点击事件
   */
  onBackTap() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 显示消息提示
   */
  showMessage(content, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content,
    });
  }
});
