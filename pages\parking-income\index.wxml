<t-message id="t-message" />

<view class="parking-income-container">
  <!-- 固定导航栏 -->
  <gradient-navbar title="车位收益" background="{{false}}" fixed="{{true}}" bind:back="onBackTap" />

  <!-- 使用浮动头部组件 -->
  <floating-header enable-collapse="{{false}}" collapse-threshold="{{80}}" id="floatingHeader">
    <!-- 主要内容（折叠时显示） -->
    <view slot="main" class="income-main">
      <!-- 总收益金额显示 (仅在总收益tab显示) -->
      <view class="income-amount" wx:if="{{currentTab === '0'}}">
        <view class="income-currency">￥</view>
        <view class="income-value">{{totalRevenue || '0.00'}}</view>
      </view>
    </view>
  </floating-header>

  <!-- Tabs选项卡 -->
  <view class="tabs-container">
    <t-tabs value="{{currentTab}}" bind:change="onTabChange" t-class="custom-tabs">
      <t-tab-panel label="总收益" value="0"></t-tab-panel>
      <t-tab-panel label="车位收益" value="1"></t-tab-panel>
    </t-tabs>
  </view>

  <!-- 内容区域 -->
  <view class="income-content">
    <t-pull-down-refresh
      value="{{refreshing}}"
      loadingProps="{{loadingProps}}"
      loadingTexts="{{['下拉刷新', '松手刷新', '正在刷新', '刷新完成']}}"
      bind:refresh="onRefresh"
    >
      <!-- 总收益列表 -->
      <view class="income-list" wx:if="{{currentTab === '0'}}">
        <block wx:if="{{totalRevenueList.length > 0}}">
          <view
            class="income-item"
            wx:for="{{totalRevenueList}}"
            wx:key="id"
            bindtap="onTotalRevenueItemTap"
            data-item="{{item}}"
          >
            <view class="item-left">
              <image class="item-icon" src="" mode="aspectFit"></image>
            </view>
            <view class="item-center">
              <view class="item-spot">{{item.location}} {{item.code}}</view>
              <view class="item-info">
                <text class="item-location">{{item.parkName}}</text>
                <text class="item-time">{{item.settleTime}}</text>
              </view>
            </view>
            <view class="item-right">
              <view class="item-amount">+￥{{item.actualRevenue}}</view>
            </view>
          </view>
        </block>

        <!-- 空状态 -->
        <block wx:else>
          <t-empty
            icon="info-circle-filled"
            description="暂无收益记录"
            wx:if="{{!loading}}"
          />
        </block>
      </view>

      <!-- 车位收益列表 -->
      <view class="income-list" wx:if="{{currentTab === '1'}}">
        <block wx:if="{{parkingSpotList.length > 0}}">
          <view
            class="income-item"
            wx:for="{{parkingSpotList}}"
            wx:key="lockId"
            bindtap="onParkingSpotItemTap"
            data-lock-id="{{item.lockId}}"
          >
            <view class="item-left">
              <image class="item-icon" src="" mode="aspectFit"></image>
            </view>
            <view class="item-center">
              <view class="item-spot">{{item.location}} {{item.code}}</view>
              <view class="item-info">
                <text class="item-location">{{item.parkName}}</text>
                <text class="item-time">总收益</text>
              </view>
            </view>
            <view class="item-right">
              <view class="item-amount">￥{{item.totalRevenue}}</view>
              <t-icon name="chevron-right" size="32rpx" color="#C8C9CC" />
            </view>
          </view>
        </block>

        <!-- 空状态 -->
        <block wx:else>
          <t-empty
            icon="info-circle-filled"
            description="暂无车位数据"
            wx:if="{{!loading}}"
          />
        </block>
      </view>

      <!-- 加载状态 -->
      <view class="loading-wrapper" wx:if="{{loading}}">
        <t-loading theme="circular" size="40rpx" text="加载中..." inherit-color />
      </view>

      <!-- 加载完成提示 -->
      <view class="no-more" wx:if="{{((currentTab === '0' && totalRevenueList.length > 0) || (currentTab === '1' && parkingSpotList.length > 0)) && !hasMoreData && !loading}}">
        没有更多数据了
      </view>
    </t-pull-down-refresh>
  </view>
</view>
