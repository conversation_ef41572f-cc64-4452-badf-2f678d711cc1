import Message from 'tdesign-miniprogram/message/index';
import {
  apiGetTotalRevenueAmount,
  apiGetTotalRevenueList,
  apiGetParkingSpotRevenueList
} from '../../api/earning';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 当前选中的tab
    currentTab: '0',
    // 总收益金额
    totalRevenue: '0.00',
    // 总收益列表
    totalRevenueList: [],
    // 车位收益列表
    parkingSpotList: [],
    // 是否正在加载
    loading: false,
    // 是否正在刷新
    refreshing: false,
    // 是否有更多数据
    hasMoreData: true,
    // 当前页码
    currentPage: 1,
    // 每页数量
    pageSize: 10,
    // 加载配置
    loadingProps: {
      size: '50rpx',
    },
    // 页面滚动位置
    scrollTop: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取收益数据
    this.fetchData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时，确保浮动头部状态正确
    setTimeout(() => {
      this.resetFloatingHeader();
    }, 300);
  },

  /**
   * 重置浮动头部状态
   */
  resetFloatingHeader() {
    const query = wx.createSelectorQuery();
    query.select('#floatingHeader').node();
    query.exec((res) => {
      if (res && res[0] && res[0].node) {
        const headerComponent = res[0].node.component;
        if (headerComponent && typeof headerComponent.setCollapsed === 'function') {
          // 先设置为展开状态
          headerComponent.setCollapsed(false);

          // 如果页面已经滚动，则根据滚动位置设置折叠状态
          if (this.data.scrollTop > 80) {
            headerComponent.setCollapsed(true);
          }
        }
      }
    });
  },

  /**
   * Tab切换事件
   */
  onTabChange(e) {
    const { value } = e.detail;
    this.setData({
      currentTab: value,
      currentPage: 1,
      hasMoreData: true
    });

    // 切换tab时重新获取数据
    this.fetchData(true);
  },

  /**
   * 获取数据
   */
  async fetchData(isRefresh = false) {
    // 如果是刷新，重置页码
    if (isRefresh) {
      this.setData({
        currentPage: 1,
        hasMoreData: true
      });

      // 根据当前tab清空对应列表
      if (this.data.currentTab === '0') {
        this.setData({ totalRevenueList: [] });
      } else {
        this.setData({ parkingSpotList: [] });
      }
    }

    // 如果没有更多数据，直接返回
    if (!this.data.hasMoreData && !isRefresh) {
      return;
    }

    // 设置加载状态
    this.setData({ loading: true });

    try {
      if (this.data.currentTab === '0') {
        // 获取总收益数据
        await this.fetchTotalRevenueData(isRefresh);
      } else {
        // 获取车位收益数据
        await this.fetchParkingSpotData(isRefresh);
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      this.showMessage('获取数据失败，请重试', 'error');
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      });
    }
  },

  /**
   * 获取总收益数据
   */
  async fetchTotalRevenueData(isRefresh = false) {
    const { currentPage, pageSize } = this.data;

    // 获取总收益金额
    if (isRefresh || currentPage === 1) {
      const totalRevenue = await apiGetTotalRevenueAmount();
      this.setData({ totalRevenue });
    }
    // 获取总收益列表
    const listData = await apiGetTotalRevenueList({
      current: currentPage,
      size: pageSize,
      model: {},
      sort: 'id',
      order: 'descending'
    });

    const newList = isRefresh ? listData.results : [...this.data.totalRevenueList, ...listData.results];
    const hasMore = parseInt(listData.curPage) < parseInt(listData.pages) - 1;

    this.setData({
      totalRevenueList: newList,
      hasMoreData: hasMore,
      currentPage: currentPage + 1
    });
  },

  /**
   * 获取车位收益数据
   */
  async fetchParkingSpotData(isRefresh = false) {
    const parkingSpotList = await apiGetParkingSpotRevenueList();

    this.setData({
      parkingSpotList,
      hasMoreData: false // 车位列表不分页
    });
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({ refreshing: true });
    this.fetchData(true);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (!this.data.loading && this.data.currentTab === '0') {
      this.fetchData();
    }
  },

  /**
   * 页面滚动事件的处理函数
   */
  onPageScroll(e) {
    // 保存滚动位置
    this.setData({ scrollTop: e.scrollTop });
  },

  /**
   * 点击总收益项
   */
  onTotalRevenueItemTap(e) {
    const { item } = e.currentTarget.dataset;
    // 可以跳转到收益详情页面
    console.log('点击总收益项:', item);
  },

  /**
   * 点击车位收益项
   */
  onParkingSpotItemTap(e) {
    const { lockId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/parking-spot-income/index?lockId=${lockId}`
    });
  },

  /**
   * 返回按钮点击事件
   */
  onBackTap() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 显示消息提示
   */
  showMessage(content, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content,
    });
  }
});
