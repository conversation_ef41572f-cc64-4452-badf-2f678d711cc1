@import '/variable.less';

/* 主容器样式 */
.parking-income-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  box-sizing: border-box;

  /* 收益主要内容 */
  .income-main {
    padding: 10rpx 30rpx 0; /* 增加顶部内边距，与导航栏保持适当距离 */
    text-align: center;
  }

  /* 收益金额显示 */
  .income-amount {
    display: flex;
    justify-content: center;
    align-items: baseline;
    margin: 20rpx 0 30rpx; /* 增加顶部和底部外边距 */

    .income-currency {
      font-size: 44rpx; /* 增大字体大小 */
      color: #FFFFFF;
      margin-right: 10rpx;
    }

    .income-value {
      font-size: 80rpx; /* 增大字体大小 */
      font-weight: 600;
      color: #FFFFFF;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }
  }

  /* 收益统计信息 */
  .income-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16rpx;
    padding: 24rpx 0;
    margin: 40rpx 30rpx 0; /* 增加顶部外边距，与金额保持适当距离 */
    backdrop-filter: blur(10rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;

    .stats-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .stats-value {
        font-size: 32rpx;
        font-weight: 500;
        color: #FFFFFF;
        margin-bottom: 8rpx;
      }

      .stats-label {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .stats-divider {
      width: 2rpx;
      height: 50rpx;
      background-color: rgba(255, 255, 255, 0.3);
    }
  }

  /* Tabs容器 */
  .tabs-container {
    background-color: #FFFFFF;
    margin: 20rpx 24rpx 0;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }

  /* 自定义tabs样式 */
  .custom-tabs {
    --td-tab-nav-bg-color: #FFFFFF;
    --td-tab-item-color: @gy2;
    // --td-tab-item-active-color: @primary;
    // --td-tab-track-color: @primary;
  }

  /* 内容区域 */
  .income-content {
    padding: 20rpx 0 0;
    margin-top: 20rpx; /* 减少顶部外边距，与tabs保持适当距离 */

    /* 收益列表 */
    .income-list {
      padding: 0 24rpx;
    }

    /* 收益项 */
    .income-item {
      display: flex;
      align-items: center;
      background-color: #FFFFFF;
      border-radius: 12rpx;
      padding: 24rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      position: relative;

      &:active {
        background-color: #F9F9F9;
      }

      .item-left {
        margin-right: 20rpx;

        .item-icon {
          width: 80rpx;
          height: 80rpx;
          border-radius: 12rpx;
          background-color: #F0F4FF;
        }
      }

      .item-center {
        flex: 1;
        overflow: hidden;

        .item-spot {
          font-size: 32rpx;
          font-weight: 500;
          color: @gy1;
          margin-bottom: 8rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .item-info {
          display: flex;
          flex-direction: column;

          .item-location {
            font-size: 26rpx;
            color: @gy2;
            margin-bottom: 4rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .item-time {
            font-size: 24rpx;
            color: #999999;
          }
        }
      }

      .item-right {
        margin-left: 16rpx;
        display: flex;
        align-items: center;

        .item-amount {
          font-size: 32rpx;
          font-weight: 500;
          color: #FA7542; /* 橙色，表示收入 */
          margin-right: 8rpx;
        }
      }
    }
  }

  /* 加载状态 */
  .loading-wrapper {
    padding: 30rpx 0;
    text-align: center;
    color: @gy2;
  }

  /* 没有更多数据提示 */
  .no-more {
    text-align: center;
    padding: 30rpx 0;
    font-size: 26rpx;
    color: #999999;
  }
}
